# 数据库表结构修复报告

## 问题描述
在运行合约分析时遇到以下错误：
```
Binder Error: Table "position_analysis" does not have a column with name "cross_margin_positions"
```

## 问题原因
position_analysis表结构不完整，缺少以下关键字段：
- `cross_margin_positions` - 全仓模式持仓数量
- `isolated_margin_positions` - 逐仓模式持仓数量  
- `task_id` - 任务ID关联字段

## 修复过程

### 1. 问题诊断
- 检查了当前数据库表结构
- 发现position_analysis表只有26个字段，缺少3个关键字段
- 发现存在依赖关系阻止ALTER TABLE操作

### 2. 依赖关系处理
- 识别出contract_risk_analysis_view视图依赖于position_analysis表
- 删除了阻止表结构修改的视图

### 3. 表结构重建
由于依赖关系复杂，采用了重建表的方式：
1. 备份现有数据（0条记录）
2. 删除现有表
3. 创建包含完整字段的新表结构
4. 恢复数据（无数据需要恢复）

### 4. 视图重建
重新创建了contract_risk_analysis_view视图以保持系统完整性

## 修复结果

### 新的position_analysis表结构（29个字段）
```sql
CREATE TABLE position_analysis (
    position_id VARCHAR(100) PRIMARY KEY,
    member_id VARCHAR(50) NOT NULL,
    contract_name VARCHAR(50) NOT NULL,
    primary_side INTEGER NOT NULL,
    open_time TIMESTAMP NOT NULL,
    close_time TIMESTAMP,
    duration_minutes DECIMAL(10,2),
    total_open_amount DECIMAL(15,2),
    total_close_amount DECIMAL(15,2),
    avg_open_price DECIMAL(15,4),
    avg_close_price DECIMAL(15,4),
    total_pnl DECIMAL(15,2),
    total_commission DECIMAL(15,2),
    net_pnl DECIMAL(15,2),
    leverage DECIMAL(8,2),
    market_orders_open INTEGER DEFAULT 0,
    limit_orders_open INTEGER DEFAULT 0,
    market_orders_close INTEGER DEFAULT 0,
    limit_orders_close INTEGER DEFAULT 0,
    cross_margin_positions INTEGER DEFAULT 0,      -- ✅ 新增
    isolated_margin_positions INTEGER DEFAULT 0,   -- ✅ 新增
    task_id VARCHAR(50),                           -- ✅ 新增
    open_trades_count INTEGER DEFAULT 0,
    close_trades_count INTEGER DEFAULT 0,
    max_position_size DECIMAL(15,2) DEFAULT 0,
    avg_position_size DECIMAL(15,2) DEFAULT 0,
    position_hold_ratio DECIMAL(5,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 功能验证
- ✅ 表结构创建成功
- ✅ 所有必需字段都已存在
- ✅ 数据插入测试通过
- ✅ 关键字段验证成功
- ✅ 视图重建完成

## 影响评估
- ✅ 无数据丢失（原表为空）
- ✅ 系统功能完整性保持
- ✅ 合约分析功能现在可以正常运行
- ✅ 所有依赖的视图已重建

## 后续建议
1. 在进行大规模数据导入前，建议先进行小批量测试
2. 定期备份数据库以防止数据丢失
3. 考虑建立数据库版本管理机制

## 修复文件
- `temp/fix_position_analysis_table.py` - 表结构修复脚本

---
**修复完成时间**: 2024-08-11 17:30  
**修复状态**: ✅ 成功  
**系统状态**: 🟢 正常运行
