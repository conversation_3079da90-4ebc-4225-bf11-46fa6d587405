#!/usr/bin/env python3
"""
从备份数据库恢复用户账户相关数据
"""

import sys
import os
import logging
import duckdb

# 添加backend路径
backend_path = os.path.join(os.path.dirname(__file__), '..', 'backend')
sys.path.insert(0, backend_path)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_backup_database():
    """查找最新的备份数据库"""
    backup_files = []
    data_dir = "data"
    
    for file in os.listdir(data_dir):
        if file.startswith("risk_analysis.duckdb.backup_"):
            backup_path = os.path.join(data_dir, file)
            backup_files.append(backup_path)
    
    if not backup_files:
        logger.error("❌ 未找到备份文件")
        return None
    
    # 返回最新的备份文件
    latest_backup = max(backup_files, key=os.path.getmtime)
    logger.info(f"找到备份文件: {latest_backup}")
    return latest_backup

def get_auth_tables_data(backup_db_path):
    """从备份数据库获取认证相关表的数据"""
    logger.info("📊 分析备份数据库中的认证数据...")
    
    auth_tables = [
        'auth_users',
        'auth_user_sessions', 
        'auth_user_activity_logs',
        'auth_system_config'
    ]
    
    backup_data = {}
    
    try:
        backup_conn = duckdb.connect(backup_db_path)
        
        for table in auth_tables:
            try:
                # 检查表是否存在
                check_sql = f"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table}'"
                result = backup_conn.execute(check_sql).fetchone()
                
                if result and result[0] > 0:
                    # 获取表数据
                    data_sql = f"SELECT * FROM {table}"
                    rows = backup_conn.execute(data_sql).fetchall()
                    
                    # 获取列信息
                    columns_sql = f"DESCRIBE {table}"
                    columns_info = backup_conn.execute(columns_sql).fetchall()
                    column_names = [col[0] for col in columns_info]
                    
                    backup_data[table] = {
                        'columns': column_names,
                        'data': rows
                    }
                    
                    logger.info(f"✅ {table}: {len(rows)} 条记录")
                else:
                    logger.warning(f"⚠️ 表 {table} 在备份中不存在")
                    
            except Exception as e:
                logger.warning(f"⚠️ 读取表 {table} 失败: {e}")
        
        backup_conn.close()
        return backup_data
        
    except Exception as e:
        logger.error(f"❌ 连接备份数据库失败: {e}")
        return {}

def restore_auth_data_to_current_db(backup_data, current_db_path):
    """将认证数据恢复到当前数据库"""
    logger.info("🔄 开始恢复认证数据到当前数据库...")
    
    try:
        current_conn = duckdb.connect(current_db_path)
        
        for table_name, table_info in backup_data.items():
            try:
                columns = table_info['columns']
                data_rows = table_info['data']
                
                if not data_rows:
                    logger.info(f"⚪ {table_name}: 无数据需要恢复")
                    continue
                
                # 清空当前表（如果有数据）
                current_conn.execute(f"DELETE FROM {table_name}")
                
                # 构建INSERT语句
                placeholders = ', '.join(['?' for _ in columns])
                insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                
                # 批量插入数据
                for row in data_rows:
                    current_conn.execute(insert_sql, row)
                
                logger.info(f"✅ {table_name}: 恢复 {len(data_rows)} 条记录")
                
            except Exception as e:
                logger.error(f"❌ 恢复表 {table_name} 失败: {e}")
                # 继续处理其他表
                continue
        
        current_conn.close()
        logger.info("✅ 认证数据恢复完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接当前数据库失败: {e}")
        return False

def verify_restored_data(current_db_path):
    """验证恢复的数据"""
    logger.info("🔍 验证恢复的认证数据...")
    
    auth_tables = [
        'auth_users',
        'auth_user_sessions', 
        'auth_user_activity_logs',
        'auth_system_config'
    ]
    
    try:
        current_conn = duckdb.connect(current_db_path)
        
        total_records = 0
        for table in auth_tables:
            try:
                count_sql = f"SELECT COUNT(*) FROM {table}"
                result = current_conn.execute(count_sql).fetchone()
                count = result[0] if result else 0
                total_records += count
                
                if count > 0:
                    logger.info(f"✅ {table}: {count} 条记录")
                else:
                    logger.info(f"⚪ {table}: 无记录")
                    
            except Exception as e:
                logger.warning(f"⚠️ 验证表 {table} 失败: {e}")
        
        # 特别检查用户账户
        try:
            users_sql = "SELECT email, username, is_active FROM auth_users"
            users = current_conn.execute(users_sql).fetchall()
            
            if users:
                logger.info("👥 恢复的用户账户:")
                for user in users:
                    email, username, is_active = user
                    status = "激活" if is_active else "禁用"
                    logger.info(f"   - {email} ({username}) - {status}")
            else:
                logger.warning("⚠️ 未找到用户账户")
                
        except Exception as e:
            logger.warning(f"⚠️ 检查用户账户失败: {e}")
        
        current_conn.close()
        
        if total_records > 0:
            logger.info(f"🎉 验证完成，共恢复 {total_records} 条认证记录")
            return True
        else:
            logger.warning("⚠️ 未恢复任何认证数据")
            return False
            
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False

def create_emergency_admin():
    """创建紧急管理员账户（如果恢复失败）"""
    logger.info("🚨 创建紧急管理员账户...")
    
    try:
        from database.duckdb_manager import DuckDBManager
        import hashlib
        import uuid
        from datetime import datetime
        
        db_manager = DuckDBManager()
        
        # 创建紧急管理员
        admin_email = "<EMAIL>"
        admin_username = "admin"
        admin_password = "admin123"  # 临时密码，登录后请立即修改
        
        # 密码哈希
        password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
        user_id = str(uuid.uuid4())
        
        # 插入管理员用户
        insert_user_sql = """
        INSERT INTO auth_users (
            id, email, username, password_hash, is_active, is_admin, 
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        now = datetime.now()
        db_manager.execute_sql_no_return(insert_user_sql, [
            user_id, admin_email, admin_username, password_hash,
            True, True, now, now
        ])
        
        logger.info("✅ 紧急管理员账户创建成功")
        logger.info(f"   邮箱: {admin_email}")
        logger.info(f"   用户名: {admin_username}")
        logger.info(f"   密码: {admin_password}")
        logger.warning("⚠️ 请登录后立即修改密码！")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建紧急管理员失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始恢复用户账户数据")
    
    current_db_path = "data/risk_analysis.duckdb"
    
    # 1. 查找备份数据库
    backup_db_path = find_backup_database()
    if not backup_db_path:
        logger.error("❌ 未找到备份文件，无法恢复")
        return False
    
    # 2. 从备份获取认证数据
    backup_data = get_auth_tables_data(backup_db_path)
    if not backup_data:
        logger.error("❌ 备份数据库中没有认证数据")
        # 创建紧急管理员
        return create_emergency_admin()
    
    # 3. 恢复数据到当前数据库
    if restore_auth_data_to_current_db(backup_data, current_db_path):
        # 4. 验证恢复结果
        if verify_restored_data(current_db_path):
            logger.info("🎉 用户账户数据恢复成功！")
            logger.info("💡 现在可以使用原来的账户登录了")
            return True
        else:
            logger.warning("⚠️ 数据恢复可能不完整，创建紧急管理员...")
            return create_emergency_admin()
    else:
        logger.error("❌ 数据恢复失败，创建紧急管理员...")
        return create_emergency_admin()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
