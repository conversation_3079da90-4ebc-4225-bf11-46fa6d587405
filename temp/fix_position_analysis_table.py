#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复position_analysis表结构 - 添加缺失的字段
"""

import sys
import os

# 添加backend路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_position_analysis_table():
    """修复position_analysis表结构，通过重建表来添加缺失的字段"""
    try:
        db_manager = DuckDBManager()

        print("🔍 检查position_analysis表当前结构...")

        # 检查当前表结构
        schema = db_manager.execute_sql("DESCRIBE position_analysis")
        if not schema:
            print("❌ position_analysis表不存在")
            return False

        # 获取现有字段列表 - 处理不同的返回格式
        existing_columns = []
        if schema:
            for row in schema:
                if isinstance(row, dict):
                    # 字典格式
                    existing_columns.append(row.get('column_name', ''))
                elif isinstance(row, (list, tuple)) and len(row) > 0:
                    # 列表/元组格式
                    existing_columns.append(row[0])
                else:
                    print(f"⚠️  未知的行格式: {type(row)} - {row}")

        print(f"📊 当前字段数量: {len(existing_columns)}")
        print(f"📋 现有字段: {existing_columns[:5]}..." if len(existing_columns) > 5 else f"📋 现有字段: {existing_columns}")

        # 检查缺失字段
        required_fields = [
            'position_id', 'member_id', 'contract_name', 'primary_side',
            'open_time', 'close_time', 'duration_minutes',
            'total_open_amount', 'total_close_amount', 'avg_open_price', 'avg_close_price',
            'total_pnl', 'total_commission', 'net_pnl', 'leverage',
            'market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close',
            'cross_margin_positions', 'isolated_margin_positions', 'task_id'
        ]

        missing_fields = [field for field in required_fields if field not in existing_columns]

        if not missing_fields:
            print("✅ position_analysis表结构已完整，无需修复")
            return True

        print(f"🔧 需要添加 {len(missing_fields)} 个缺失字段: {missing_fields}")

        # 由于有依赖关系，我们需要重建表
        print("⚠️  检测到表依赖关系，将通过重建表来修复结构...")

        # 1. 备份现有数据
        print("💾 备份现有数据...")
        backup_data = db_manager.execute_sql("SELECT * FROM position_analysis")
        print(f"📊 备份了 {len(backup_data) if backup_data else 0} 条记录")

        # 2. 删除现有表（这会自动删除依赖的视图）
        print("🗑️  删除现有表...")
        db_manager.execute_sql("DROP TABLE IF EXISTS position_analysis")

        # 3. 创建新的完整表结构
        print("🔨 创建新的完整表结构...")
        create_table_sql = """
        CREATE TABLE position_analysis (
            position_id VARCHAR(100) PRIMARY KEY,
            member_id VARCHAR(50) NOT NULL,
            contract_name VARCHAR(50) NOT NULL,
            primary_side INTEGER NOT NULL,
            open_time TIMESTAMP NOT NULL,
            close_time TIMESTAMP,
            duration_minutes DECIMAL(10,2),
            total_open_amount DECIMAL(15,2),
            total_close_amount DECIMAL(15,2),
            avg_open_price DECIMAL(15,4),
            avg_close_price DECIMAL(15,4),
            total_pnl DECIMAL(15,2),
            total_commission DECIMAL(15,2),
            net_pnl DECIMAL(15,2),
            leverage DECIMAL(8,2),
            market_orders_open INTEGER DEFAULT 0,
            limit_orders_open INTEGER DEFAULT 0,
            market_orders_close INTEGER DEFAULT 0,
            limit_orders_close INTEGER DEFAULT 0,
            cross_margin_positions INTEGER DEFAULT 0,
            isolated_margin_positions INTEGER DEFAULT 0,
            task_id VARCHAR(50),
            open_trades_count INTEGER DEFAULT 0,
            close_trades_count INTEGER DEFAULT 0,
            max_position_size DECIMAL(15,2) DEFAULT 0,
            avg_position_size DECIMAL(15,2) DEFAULT 0,
            position_hold_ratio DECIMAL(5,4) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """

        db_manager.execute_sql(create_table_sql)
        print("✅ 新表结构创建成功")

        # 4. 恢复数据（只恢复兼容的字段）
        if backup_data:
            print("📥 恢复备份数据...")

            # 获取新表的字段列表
            new_schema = db_manager.execute_sql("DESCRIBE position_analysis")
            new_columns = []
            for row in new_schema:
                if isinstance(row, dict):
                    new_columns.append(row.get('column_name', ''))
                elif isinstance(row, (list, tuple)) and len(row) > 0:
                    new_columns.append(row[0])

            # 准备插入数据
            restored_count = 0
            for row_data in backup_data:
                try:
                    # 只插入兼容的字段
                    if isinstance(row_data, dict):
                        # 过滤出新表中存在的字段
                        filtered_data = {k: v for k, v in row_data.items() if k in new_columns}

                        # 构建插入SQL
                        columns = list(filtered_data.keys())
                        placeholders = ['?' for _ in columns]
                        values = list(filtered_data.values())

                        insert_sql = f"INSERT INTO position_analysis ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                        db_manager.execute_sql(insert_sql, values)
                        restored_count += 1

                except Exception as e:
                    print(f"⚠️  恢复单条记录失败: {e}")
                    continue

            print(f"✅ 成功恢复 {restored_count} 条记录")

        return True
                
        # 验证修复结果
        print("\n🔍 验证修复结果...")
        updated_schema = db_manager.execute_sql("DESCRIBE position_analysis")
        updated_columns = []
        if updated_schema:
            for row in updated_schema:
                if isinstance(row, dict):
                    updated_columns.append(row.get('column_name', ''))
                elif isinstance(row, (list, tuple)) and len(row) > 0:
                    updated_columns.append(row[0])
        
        print(f"📊 修复后字段数量: {len(updated_columns)}")
        
        # 检查所有必需字段是否存在
        required_fields = [
            'position_id', 'member_id', 'contract_name', 'primary_side',
            'open_time', 'close_time', 'duration_minutes',
            'total_open_amount', 'total_close_amount', 'avg_open_price', 'avg_close_price',
            'total_pnl', 'total_commission', 'net_pnl', 'leverage',
            'market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close',
            'cross_margin_positions', 'isolated_margin_positions', 'task_id'
        ]
        
        missing_required = [field for field in required_fields if field not in updated_columns]
        
        if missing_required:
            print(f"⚠️  仍有缺失的必需字段: {missing_required}")
            return False
        else:
            print("🎉 position_analysis表结构修复完成！")
            print("✅ 所有必需字段都已存在")
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_table_structure():
    """显示修复后的表结构"""
    try:
        db_manager = DuckDBManager()
        
        print("\n📋 position_analysis表完整结构:")
        print("=" * 80)
        
        schema = db_manager.execute_sql("DESCRIBE position_analysis")
        if schema:
            print(f"{'字段名':<30} {'类型':<20} {'可空':<10}")
            print("-" * 80)
            for row in schema:
                if isinstance(row, dict):
                    field_name = row.get('column_name', '')
                    field_type = row.get('column_type', '')
                    nullable = row.get('null', 'YES')
                elif isinstance(row, (list, tuple)) and len(row) >= 2:
                    field_name = row[0]
                    field_type = row[1]
                    nullable = row[2] if len(row) > 2 else 'YES'
                else:
                    continue
                print(f"{field_name:<30} {field_type:<20} {nullable:<10}")
        else:
            print("❌ 无法获取表结构")
            
    except Exception as e:
        print(f"❌ 显示表结构失败: {e}")

if __name__ == "__main__":
    print("🔧 开始修复position_analysis表结构...")
    
    success = fix_position_analysis_table()
    
    if success:
        show_table_structure()
        print("\n✅ 修复完成！现在可以正常插入数据了。")
    else:
        print("\n❌ 修复失败，请检查错误信息。")
        sys.exit(1)
