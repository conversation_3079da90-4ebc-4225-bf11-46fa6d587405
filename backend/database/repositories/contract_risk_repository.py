"""
合约风险分析结果数据仓库 - 升级版
支持新旧存储结构的智能查询和数据格式修复

⚠️ 废弃警告: 此模块即将废弃，建议使用 AlgorithmStorageManager 替代
📅 废弃时间: 2025-07-01 开始分阶段废弃
🔄 迁移指南: 使用 modules.contract_risk_analysis.services.data_adapter.ContractDataAdapter
"""

import warnings
import json
import pandas as pd
from datetime import datetime

# 发出废弃警告
warnings.warn(
    "ContractRiskRepository is deprecated and will be removed in future versions. "
    "Use AlgorithmStorageManager via ContractDataAdapter instead.",
    DeprecationWarning,
    stacklevel=2
)

from database.duckdb_manager import db_manager
from database.algorithm_storage_manager import AlgorithmStorageManager

def json_serializable(obj):
    """
    将对象转换为JSON可序列化的格式
        处理pandas Timestamp、datetime、set、dataclass等特殊类型
    """
    if hasattr(obj, 'isoformat'):  # datetime, Timestamp等
        return obj.isoformat()
    elif hasattr(obj, 'item'):  # numpy数值类型
        return obj.item()
    elif isinstance(obj, set):  # set类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, frozenset):  # frozenset类型转换为列表
        return [json_serializable(item) for item in obj]
    elif hasattr(obj, '__dict__'):  # dataclass或其他对象，转换为字典
        return {k: json_serializable(v) for k, v in obj.__dict__.items()}
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    elif pd.isna(obj):  # pandas NaN值
        return None
    else:
        return obj

class ContractRiskRepository:
    def __init__(self):
        self.db_manager = db_manager
        # 新版存储管理器
        self.storage_manager = AlgorithmStorageManager()
        # 自动检测是否有新存储结构
        self._has_new_storage = self._check_new_storage_exists()
        
    def _check_new_storage_exists(self):
        """检查是否存在新存储结构"""
        try:
            # 安全地检查表是否存在
            # 使用DuckDB的信息模式查询表是否存在
            result = self.db_manager.execute_sql("""
                SELECT COUNT(*) as table_count 
                FROM information_schema.tables 
                WHERE table_name = 'algorithm_results'
            """)
            return result and result[0]['table_count'] > 0
        except Exception as e:
            # 如果查询失败，说明新存储结构不存在或有问题
            return False
    
    def save_analysis_result(self, task_id, analysis_type, filename, total_contracts, 
                           risk_contracts, wash_trading_count, cross_bd_count, result_data):
        """保存合约风险分析结果 - 智能存储"""
        try:
            # 原有存储方式（保持兼容）
            self._save_to_legacy_storage(task_id, analysis_type, filename, total_contracts, 
                                       risk_contracts, wash_trading_count, cross_bd_count, result_data)
            
            # 如果有新存储结构，同时保存到新表
            if self._has_new_storage:
                self._save_to_new_storage(task_id, result_data)
                
            return True
            
        except Exception as e:
            return False
    
    def _save_to_legacy_storage(self, task_id, analysis_type, filename, total_contracts, 
                              risk_contracts, wash_trading_count, cross_bd_count, result_data):
        """保存到原有存储结构"""
        serializable_data = json_serializable(result_data)
        result_json = json.dumps(serializable_data, ensure_ascii=False)
        
        sql = """
            INSERT INTO contract_risk_analysis 
            (task_id, analysis_type, filename, total_contracts, risk_contracts, 
             wash_trading_count, cross_bd_count, result_data, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = [
            task_id, analysis_type, filename, total_contracts, risk_contracts,
            wash_trading_count, cross_bd_count, result_json,
            datetime.now().isoformat(), datetime.now().isoformat()
        ]
        
        self.db_manager.execute_sql(sql, params)
    
    def _save_to_new_storage(self, task_id, result_data):
        """保存到新存储结构"""
        try:
            # 使用新存储管理器保存数据 - 修复方法名
            result_id = self.storage_manager.store_algorithm_result(
                task_id=task_id, 
                algorithm_type='suspected_wash_trading',  # 默认算法类型
                result_data=result_data
            )
        except Exception as e:
            pass  # 静默处理新存储失败
    
    def get_analysis_result(self, task_id, user_id=None):
        """获取合约风险分析结果 - 智能查询（支持用户过滤）"""
        try:
            # 优先从新存储查询（性能更好）
            if self._has_new_storage:
                new_result = self._get_from_new_storage(task_id, user_id=user_id)
                # 检查新存储的数据是否完整（contract_risks不为空）
                if new_result and new_result.get('contract_risks') and len(new_result['contract_risks']) > 0:
                    return self._format_for_compatibility(task_id, new_result)
            
            # 回退到原有存储
            return self._get_from_legacy_storage(task_id)
            
        except Exception as e:
            return None
    
    def _get_from_legacy_storage(self, task_id):
        """从原有存储获取"""
        sql = "SELECT * FROM contract_risk_analysis WHERE task_id = ?"
        results = self.db_manager.execute_sql(sql, [task_id])
        
        if results:
            result_dict = results[0]
            # 解析JSON数据
            if result_dict.get('result_data'):
                try:
                    result_dict['result_data'] = json.loads(result_dict['result_data'])
                except json.JSONDecodeError:
                    result_dict['result_data'] = {}
            
            # 修复数据格式问题
            if result_dict.get('result_data', {}).get('contract_risks'):
                result_dict['result_data']['contract_risks'] = self.fix_wash_trading_format(
                    result_dict['result_data']['contract_risks']
                )
            
            return result_dict
        
        return None
    
    def _get_from_new_storage(self, task_id, user_id=None):
        """从新存储获取"""
        return self.storage_manager.query_algorithm_results(task_id, user_id=user_id)
    
    def _format_for_compatibility(self, task_id, new_result):
        """将新存储结果格式化为兼容格式"""
        return {
            'task_id': task_id,
            'analysis_type': 'contract_risk_analysis',
            'status': 'completed',
            'result_data': new_result,
            'total_contracts': new_result.get('total_analyzed', 0),
            'risk_contracts': new_result.get('risks_found', 0),
            'wash_trading_count': len([r for r in new_result.get('contract_risks', []) 
                                     if 'wash_trading' in r.get('detection_type', '')]),
            'cross_bd_count': len([r for r in new_result.get('contract_risks', []) 
                                 if 'cross_bd' in r.get('detection_method', '')]),
            'created_at': new_result.get('created_at'),
            'updated_at': new_result.get('updated_at')
        }
    
    def get_contract_risks(self, task_id, algorithm_type=None, contract_name=None, 
                          risk_level=None, page=1, page_size=50):
        """获取合约风险列表 - 高性能查询"""
        try:
            analysis_result = self.get_analysis_result(task_id)
            if not analysis_result:
                return {'risks': [], 'total': 0, 'page': page, 'page_size': page_size}
            
            result_data = analysis_result.get('result_data', {})
            all_risks = result_data.get('contract_risks', [])
            
            # 应用过滤条件
            filtered_risks = self._apply_filters(all_risks, algorithm_type, contract_name, risk_level)
            
            # 分页处理
            total = len(filtered_risks)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paged_risks = filtered_risks[start_idx:end_idx]
            
            return {
                'risks': paged_risks,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
            
        except Exception as e:
            return {'risks': [], 'total': 0, 'page': page, 'page_size': page_size}
    
    def _apply_filters(self, risks, algorithm_type, contract_name, risk_level):
        """应用过滤条件"""
        filtered = []
        for risk in risks:
            # 算法类型过滤
            if algorithm_type and risk.get('detection_type') != algorithm_type:
                continue
            # 合约名称过滤
            if contract_name and risk.get('contract_name') != contract_name:
                continue
            # 风险等级过滤
            if risk_level and risk.get('risk_level') != risk_level:
                continue
            filtered.append(risk)
        return filtered
    
    def get_wash_trading_data(self, task_id=None, wash_type=None):
        """获取对敲交易数据 - 专门查询"""
        try:
            if task_id:
                # 单任务查询
                analysis_result = self.get_analysis_result(task_id)
                if not analysis_result:
                    return []
                result_data = analysis_result.get('result_data', {})
                all_risks = result_data.get('contract_risks', [])
            else:
                # 全局查询（性能较差，建议优化）
                all_risks = self._get_all_wash_trading_risks()
            
            # 筛选对敲交易
            return self._filter_wash_trading_risks(all_risks, wash_type)
            
        except Exception as e:
            return []
    
    def _get_all_wash_trading_risks(self):
        """获取所有对敲交易风险"""
        if self._has_new_storage:
            # 从新存储高效查询
            return self.storage_manager.query_wash_trading_all()
        else:
            # 从原存储查询
            results = self.db_manager.execute_sql("""
                SELECT result_data FROM contract_risk_analysis 
                WHERE result_data IS NOT NULL
            """)
            all_risks = []
            for result in results:
                result_data = json.loads(result['result_data']) if isinstance(result['result_data'], str) else result['result_data']
                all_risks.extend(result_data.get('contract_risks', []))
            return all_risks
    
    def _filter_wash_trading_risks(self, risks, wash_type):
        """筛选对敲交易风险"""
        wash_risks = []
        for risk in risks:
            detection_type = risk.get('detection_type', '')
            detection_method = risk.get('detection_method', '')
            
            # 判断是否为对敲交易
            is_wash_trading = (
                detection_type == 'suspected_wash_trading' or
                'wash_trading' in detection_method or
                'wash' in detection_type.lower()
            )
            
            if is_wash_trading:
                # 类型筛选
                if wash_type:
                    if wash_type == 'same_account' and 'same_account' not in detection_method:
                        continue
                    elif wash_type == 'cross_account' and 'cross_account' not in detection_method:
                        continue
                
                wash_risks.append(risk)
        
        return wash_risks
    
    def fix_wash_trading_format(self, contract_risks):
        """修复对敲交易数据格式 - 核心数据修复方法"""
        if not contract_risks:
            return []
        
        fixed_risks = []
        for risk in contract_risks:
            fixed_risk = risk.copy()
            
            # 1. 统一detection_method字段
            detection_type = risk.get('detection_type', '')
            detection_method = risk.get('detection_method', '')
            
            if not detection_method or detection_method == 'None':
                if detection_type == 'suspected_wash_trading':
                    # 根据counterparty_ids判断类型
                    counterparty_ids = risk.get('counterparty_ids', [])
                    if not counterparty_ids or (len(counterparty_ids) == 1 and counterparty_ids[0] == risk.get('member_id')):
                        fixed_risk['detection_method'] = 'same_account_wash_trading'
                    else:
                        fixed_risk['detection_method'] = 'cross_account_wash_trading'
                else:
                    fixed_risk['detection_method'] = detection_type
            
            # 2. 统一字段映射
            # 用户ID字段统一
            if 'user_id' in risk and not risk.get('member_id'):
                fixed_risk['member_id'] = risk['user_id']
            elif 'memberId' in risk and not risk.get('member_id'):
                fixed_risk['member_id'] = risk['memberId']
            
            # 合约名称字段统一
            if 'contractName' in risk and not risk.get('contract_name'):
                fixed_risk['contract_name'] = risk['contractName']
            
            # 交易量字段统一
            if 'abnormalVolume' in risk and not risk.get('abnormal_volume'):
                fixed_risk['abnormal_volume'] = risk['abnormalVolume']
            
            # 交易次数字段统一
            if 'transaction_count' in risk and not risk.get('trade_count'):
                fixed_risk['trade_count'] = risk['transaction_count']
            elif 'trades_count' in risk and not risk.get('trade_count'):
                fixed_risk['trade_count'] = risk['trades_count']
            
            # 风险等级字段统一
            if 'risk_level' in risk and not risk.get('severity'):
                risk_level = risk['risk_level']
                if risk_level in ['high', 'medium']:
                    fixed_risk['severity'] = risk_level
                else:
                    fixed_risk['severity'] = 'medium'
            
            # 3. 确保关键字段存在
            if 'counterparty_ids' not in fixed_risk:
                fixed_risk['counterparty_ids'] = []
            elif not isinstance(fixed_risk['counterparty_ids'], list):
                counterparty = fixed_risk['counterparty_ids']
                fixed_risk['counterparty_ids'] = [counterparty] if counterparty else []
            
            # 4. 为same_account类型补充counterparty_ids
            if detection_type == 'same_account' and not fixed_risk.get('counterparty_ids'):
                member_id = fixed_risk.get('member_id') or fixed_risk.get('user_id')
                if member_id:
                    fixed_risk['counterparty_ids'] = [member_id]
            
            fixed_risks.append(fixed_risk)
        
        return fixed_risks
    
    def update_analysis_result(self, task_id, analysis_type=None, filename=None, 
                             total_contracts=None, risk_contracts=None, 
                             wash_trading_count=None, cross_bd_count=None, result_data=None):
        """更新合约风险分析结果"""
        try:
            # 构建动态更新SQL
            update_fields = []
            params = []
            
            if analysis_type is not None:
                update_fields.append("analysis_type = ?")
                params.append(analysis_type)
            
            if filename is not None:
                update_fields.append("filename = ?")
                params.append(filename)
            
            if total_contracts is not None:
                update_fields.append("total_contracts = ?")
                params.append(total_contracts)
            
            if risk_contracts is not None:
                update_fields.append("risk_contracts = ?")
                params.append(risk_contracts)
            
            if wash_trading_count is not None:
                update_fields.append("wash_trading_count = ?")
                params.append(wash_trading_count)
            
            if cross_bd_count is not None:
                update_fields.append("cross_bd_count = ?")
                params.append(cross_bd_count)
            
            if result_data is not None:
                update_fields.append("result_data = ?")
                serializable_data = json_serializable(result_data)
                params.append(json.dumps(serializable_data, ensure_ascii=False))
            
            if not update_fields:
                return False  # 没有字段需要更新
            
            # 添加updated_at字段
            update_fields.append("updated_at = ?")
            params.append(datetime.now().isoformat())
            
            # 添加WHERE条件
            params.append(task_id)
            
            sql = f"UPDATE contract_risk_analysis SET {', '.join(update_fields)} WHERE task_id = ?"
            
            self.db_manager.execute_sql(sql, params)
            return True
            
        except Exception as e:
            return False
    
    def get_all_analysis_results(self, limit=100, offset=0):
        """获取所有合约风险分析结果"""
        try:
            sql = """
                SELECT task_id, analysis_type, filename, total_contracts, risk_contracts,
                       wash_trading_count, cross_bd_count, created_at, updated_at
                FROM contract_risk_analysis 
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """
            
            results = self.db_manager.execute_sql(sql, [limit, offset])
            return results  # execute_sql已经返回字典列表
            
        except Exception as e:
            return []
    
    def delete_analysis_result(self, task_id):
        """删除合约风险分析结果"""
        try:
            sql = "DELETE FROM contract_risk_analysis WHERE task_id = ?"
            self.db_manager.execute_sql(sql, [task_id])
            return True
            
        except Exception as e:
            return False
    
    def get_statistics(self):
        """获取合约风险分析统计信息"""
        try:
            sql = """
                SELECT 
                    COUNT(*) as total_analyses,
                    SUM(total_contracts) as total_contracts_analyzed,
                    SUM(risk_contracts) as total_risks_found,
                    SUM(wash_trading_count) as total_wash_trading,
                    SUM(cross_bd_count) as total_cross_bd,
                    AVG(CAST(risk_contracts AS FLOAT) / NULLIF(total_contracts, 0) * 100) as avg_risk_rate
                FROM contract_risk_analysis
            """
            
            results = self.db_manager.execute_sql(sql)
            
            if results:
                return results[0]  # execute_sql已经返回字典列表
            
            return {}
            
        except Exception as e:
            return {}
    
    def get_completed_tasks(self):
        """获取已完成的合约分析任务列表"""
        try:
            sql = """
                SELECT task_id, analysis_type, filename, total_contracts, risk_contracts,
                       wash_trading_count, cross_bd_count, created_at, updated_at
                FROM contract_risk_analysis 
                ORDER BY created_at DESC
            """
            
            results = self.db_manager.execute_sql(sql)
            return results if results else []
            
        except Exception as e:
            return []
    
    def get_contract_risk_analysis_result(self, task_id):
        """获取合约风险分析结果（链路分析使用的格式）"""
        try:
            analysis_result = self.get_analysis_result(task_id)
            if not analysis_result:
                return None
            
            # 返回result_data部分，这是链路分析期望的格式
            result_data = analysis_result.get('result_data', {})
            
            # 确保contract_risks字段存在
            if 'contract_risks' not in result_data:
                result_data['contract_risks'] = []
            
            return result_data
            
        except Exception as e:
            return None
    
    def get_summary_statistics(self, task_id):
        """获取汇总统计 - 升级版，包含动态计算的异常类型分布和风险等级分布"""
        try:
            analysis_result = self.get_analysis_result(task_id)
            if not analysis_result:
                return {}
            
            result_data = analysis_result.get('result_data', {})
            summary = result_data.get('summary', {})
            
            # 获取合约风险数据来计算分布
            contract_risks = result_data.get('contract_risks', [])
            
            # 动态计算异常类型分布（中文名称）
            anomaly_distribution = self._calculate_anomaly_distribution(contract_risks)
            
            # 动态计算风险等级分布
            risk_distribution = self._calculate_risk_distribution(contract_risks)
            
            return {
                'total_analyzed': result_data.get('total_analyzed', 0),
                'risks_found': result_data.get('risks_found', 0),
                'wash_trading_count': analysis_result.get('wash_trading_count', 0),
                'cross_bd_count': analysis_result.get('cross_bd_count', 0),
                'risk_distribution': risk_distribution,  # 使用动态计算的结果
                'anomaly_distribution': anomaly_distribution,  # 使用动态计算的结果
                'contract_distribution': summary.get('contract_distribution', {})
            }
            
        except Exception as e:
            return {}
    
    def _calculate_anomaly_distribution(self, contract_risks):
        """计算异常类型分布（返回中文名称）- 区分同账户和跨账户对敲"""
        # 异常类型英文到中文的映射表
        anomaly_type_mapping = {
            'high_frequency_trading': '高频交易',

            'regular_brush_trading': '规律性刷量',
            'wash_trading': '对敲交易',
            'funding_rate_arbitrage': '资金费率交易',
            'suspected_wash_trading': '对敲交易'
        }
        
        anomaly_distribution = {}
        
        for risk in contract_risks:
            # 获取异常类型和检测方法
            detection_type = risk.get('detection_type') or risk.get('anomaly_type', '未知类型')
            detection_method = risk.get('detection_method', '')
            
            # 对于对敲类型，进一步区分同账户和跨账户
            if detection_type in ['wash_trading', 'suspected_wash_trading']:
                if 'same_account' in detection_method:
                    chinese_type = '同账户对敲'
                elif 'cross_account' in detection_method or 'cross_bd' in detection_method:
                    chinese_type = '跨账户对敲'
                else:
                    # 如果没有明确的检测方法，尝试从对手方ID判断
                    counterparty_ids = risk.get('counterparty_ids', [])
                    member_id = risk.get('member_id', '')
                    
                    if not counterparty_ids or (len(counterparty_ids) == 1 and counterparty_ids[0] == member_id):
                        chinese_type = '同账户对敲'
                    else:
                        chinese_type = '跨账户对敲'
            else:
                # 转换为中文名称
                chinese_type = anomaly_type_mapping.get(detection_type, detection_type)
            
            # 计数
            anomaly_distribution[chinese_type] = anomaly_distribution.get(chinese_type, 0) + 1
        
        return anomaly_distribution
    
    def _calculate_risk_distribution(self, contract_risks):
        """计算风险等级分布"""
        risk_distribution = {'high': 0, 'medium': 0, 'low': 0}
        
        if not contract_risks:
            return risk_distribution
        
        for risk in contract_risks:
            # 检查多个可能的风险等级字段
            risk_level = risk.get('risk_level') or risk.get('severity') or 'medium'
            
            # 统一映射风险等级
            if str(risk_level).lower() in ['高', 'high', 'critical', '极高风险']:
                risk_distribution['high'] += 1
            elif str(risk_level).lower() in ['低', 'low']:
                risk_distribution['low'] += 1
            elif str(risk_level).lower() in ['中', 'medium', 'normal']:
                risk_distribution['medium'] += 1
            else:
                # 默认为中风险
                risk_distribution['medium'] += 1
        
        return risk_distribution

# 创建全局实例
contract_risk_repository = ContractRiskRepository() 